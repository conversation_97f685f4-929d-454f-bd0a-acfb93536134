import { Injectable } from '@nestjs/common';
import { Op } from 'sequelize';
import { EmiEntity } from 'src/database/pg/entities/emi.entity';
import { loanTransaction } from 'src/database/pg/entities/loanTransaction';
import { PgService } from 'src/database/pg/pg.service';
import { DateService } from 'src/utils/date.service';

@Injectable()
export class AnalyticsService {
  constructor(
    private readonly pg: PgService,
    private readonly dateService: DateService,
  ) {}

  //#region funPaidEmiAnalytics
  async funPaidEmiAnalytics(query) {
    let month = query?.month;
    let startDate = new Date();
    startDate.setDate(1);
    let endDate: Date;
    if (month || month == 0) {
      if (month > startDate.getMonth()) {
        startDate.setFullYear(startDate.getFullYear() - 1);
      }
      startDate.setMonth(month);
    }
    endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
    startDate = this.dateService.getGlobalDate(startDate);
    endDate = this.dateService.getGlobalDate(endDate);

    const allEmiData = await this.pg.findAll(EmiEntity, {
      attributes: ['loanId', 'emi_date', 'payment_done_date'],
      include: [
        {
          model: loanTransaction,
          attributes: ['interestRate'],
          required: true,
        },
      ],
      where: {
        emi_date: { [Op.gte]: startDate, [Op.lt]: endDate },
        payment_done_date: { [Op.ne]: null },
        payment_status: '1',
      },
      raw: false,
    });

    const allAprs: number[] = [];
    let totalPaidEmisCount = 0;

    allEmiData.forEach((ele) => {
      const loan = ele?.loan;
      if (loan) {
        if (loan?.interestRate) {
          const apr = +(loan?.interestRate * 365).toFixed(2);
          allAprs.push(apr);
          totalPaidEmisCount++;
        }
      }
    });
    return this.generateAprBins(allAprs, totalPaidEmisCount, 'emi');
  }
  //#endregion

  //#region funDisbursedLoanCount
  async funDisbursedLoanAnalytics(query) {
    let month = query?.month;
    let startDate = new Date();
    startDate.setDate(1);
    let endDate: Date;
    if (month || month == 0) {
      if (month > startDate.getMonth()) {
        startDate.setFullYear(startDate.getFullYear() - 1);
      }
      startDate.setMonth(month);
    }
    endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
    startDate = this.dateService.getGlobalDate(startDate);
    endDate = this.dateService.getGlobalDate(endDate);

    const loanData = await this.pg.findAll(loanTransaction, {
      attributes: ['id', 'interestRate', 'loanDisbursementDateTime'],
      where: {
        loanDisbursementDateTime: { [Op.gte]: startDate, [Op.lt]: endDate },
        loanStatus: { [Op.in]: ['Active', 'Complete'] },
      },
      raw: true,
    });

    const allAprs: number[] = [];
    let totalLoansCount = 0;

    loanData.forEach((ele) => {
      const interestRate = ele?.interestRate;
      if (interestRate) {
        const apr = +(interestRate * 365).toFixed(2);
        allAprs.push(apr);
        totalLoansCount++;
      }
    });
    return this.generateAprBins(allAprs, totalLoansCount, 'loan');
  }
  //#endregion

  //#region generateAprBins
  private generateAprBins(
    aprValues: number[],
    totalCount: number = 0,
    type: 'emi' | 'loan' = 'emi',
  ) {
    if (!aprValues?.length || totalCount === 0) {
      return { total: 0 };
    }
    const binSize = 10;
    const minApr = Math.min(...aprValues);
    const maxApr = Math.max(...aprValues);
    const startBin = Math.floor(minApr / binSize) * binSize;
    const endBin = Math.ceil(maxApr / binSize) * binSize;
    const binData = {};

    for (let i = startBin; i < endBin; i += binSize) {
      const binMax = i + binSize - 1;
      const binKey = `${i}-${binMax}`;

      const binCount = aprValues.filter(
        (apr) => apr >= i && apr <= binMax,
      ).length;

      if (binCount > 0) {
        const percentage = +((binCount / totalCount) * 100).toFixed(2);
        const fieldName = type === 'loan' ? 'totalLoans' : 'totalEmi';
        binData[binKey] = { percentage, [fieldName]: binCount };
      }
    }

    const result = {
      ...binData,
      total: totalCount,
    };
    return result;
  }
  //#endregion
}
