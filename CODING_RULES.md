# Coding Standards & Rules - Admin Backend

## **1. File Structure & Naming Conventions**

### File Naming

- **camelCase** for file names: `loanTransaction.ts`, `dataCodesService.ts`
- Entity files: `entityName.entity.ts` or `entityName.ts`
- Service files: `serviceName.service.ts`
- Controller files: `controllerName.controller.ts`
- Interface files: `interfaceName.interface.ts`
- Query files: `serviceName.query.ts`

### Class Naming

- **PascalCase** for classes: `LoanTransaction`, `DataCodesService`
- Entity classes can be **camelCase**: `loanTransaction`, `registeredUsers`

## **2. Import/Export Patterns**

### Import Structure

```typescript
// Imports (always start with this comment)
import { Injectable } from '@nestjs/common';
import { ApiService } from 'src/utils/api.service';
import { nDataCodes } from 'src/constant/networks';
```

### Export Patterns

```typescript
export const PG_CORE_ENTITIES = [
  admin,
  BankingEntity,
  // ... other entities
];

export class ServiceName {
  // class implementation
}
```

## **3. Variable Declaration & Naming**

### Variable Naming

- **camelCase** for variables: `loanData`, `finalizedList`, `isReadOnly`
- **UPPER_SNAKE_CASE** for constants: `SYSTEM_ADMIN_ID`, `DATA_CODES_CLICKHOUSE_TABLE`
- **camelCase** for object properties: `netApprovedAmount`, `interestRate`

### Variable Declaration

```typescript
// Use const for immutable values
const loanData = await this.pg.findOne(loanTransaction, options);
const finalizedData = {};

// Use let for mutable values
let isGoodAccFound = false;
let total_accounts = 0;
```

## **4. Function/Method Patterns**

### Method Naming

- **camelCase**: `calculateInternalScore`, `formatAfterFind`
- Prefix with `fun` for controller methods: `funCalculateInternalScore`

### Method Structure

```typescript
async methodName(reqData) {
  const isReadOnly = reqData.readOnly == 'true' || reqData.readOnly == true;

  // Gathering -> Data (use comments for sections)
  const targetData = await this.query.dataForSomething(reqData);

  if (isReadOnly) {
    return { isReadOnly, targetData };
  }

  // Processing logic here
  return { success: true, data: response };
}
```

## **5. Loop Patterns**

### forEach for iteration

```typescript
instances.forEach((instance: any) => {
  if (instance.interestRate) {
    instance.interestRate = +instance.interestRate;
  }
});

reqData.targetList.forEach((el) => {
  finalizedData[el] = decryptText(el);
});
```

### for loop for indexed iteration

```typescript
for (let index = 0; index < data.rows.length; index++) {
  await this.backendService.experianSoftHit(
    data.rows[index].uniqueId.toString(),
  );
}
```

### map for transformations

```typescript
const loanIds = emiList.map((el) => el.loanId);
const updated = update[1]?.map((el) => {
  try {
    return el.get({ plain: true });
  } catch (error) {
    return el;
  }
});
```

### filter for filtering

```typescript
cibilIds = cibilIds.filter((el) => el);
```

## **6. Conditional Patterns**

### if-else structure

```typescript
if (Array.isArray(instances)) {
  if (instances.length > 0) {
    // nested logic
  }
} else if (instances) {
  // single instance logic
}
```

### Switch statements

```typescript
switch (reqData.id) {
  case 'HIGH_LEAD_SCORE':
    raw_data = await this.query.dataForHighLeadScore(reqData);
    break;

  case 'HIGH_LEAD_SCORE_UNTOUCHED':
    raw_data = await this.query.dataForHighLeadScoreUnTouched(reqData);
    break;

  default:
    break;
}
```

### Ternary operators

```typescript
const options = t1 ? { transaction: t1 } : {};
const nextPage = totalPages == page ? null : page + 1;
```

## **7. Error Handling Patterns**

### Try-catch blocks

```typescript
try {
  const created = await repo.create(data, options);
  return created['dataValues'];
} catch (error) {
  console.log({ error });
  this.logger.error(error + ' In Table:-> ' + repo.getTableName());
  if (error.name === 'SequelizeUniqueConstraintError')
    throw new HTTPError({ statusCode: HttpStatus.CONFLICT });
  throw new HTTPError({ statusCode: HttpStatus.INTERNAL_SERVER_ERROR });
}
```

### Parameter validation

```typescript
if (!query) {
  raiseParamMissing('query');
}
if (!repo) {
  throw new HTTPError({ statusCode: HttpStatus.INTERNAL_SERVER_ERROR });
}
```

### API response handling

```typescript
if (response.status == HttpStatusCode.Ok) {
  return response.response.output ?? [];
} else {
  throw HTTPError({
    message: 'Failed Data codes api -> ' + nDataCodes.readQuery,
  });
}
```

## **8. Async/Await Patterns**

### Always use async/await

```typescript
async methodName() {
  const response = await this.dataCodes.paymentForecast();
  return { success: true, data: response };
}

// For API calls
const response = await this.api.post(url, body, null, null, { headers });
```

## **9. Object & Array Manipulation**

### Object creation

```typescript
const finalizedData = {};
const loanData = {};

// Object assignment in loops
loanList.forEach((el) => {
  loanData[el.id] = el;
});
```

### Array operations

```typescript
// Converting arrays to objects for performance
const loanData = {};
loanList.forEach((el) => {
  loanData[el.id] = el;
});

// Chunking arrays
for (let i = 0; i < array.length; i += n) arrays.push(array.slice(i, i + n));
```

## **10. Database Query Patterns**

### Query options structure

```typescript
const loanOptions: ISequelizeFindOptions = {
  attributes: ['id', 'cibilId', 'completedLoan'],
  where: { id: loanIds },
  limit: audiance_limit,
};

const loanList = await this.pg.findAll(loanTransaction, loanOptions);
```

### Include patterns

```typescript
const bankingInclude: Includeable = {
  attributes: ['adminSalary', 'salary'],
  model: BankingEntity,
};
```

## **11. Response Formatting**

### Standard response format

```typescript
return { success: true, data: response };
return { success: true, finalizedData };
return { isReadOnly, targetData };
```

### Error responses

```typescript
throw HTTPError({
  message: 'Failed Data codes api -> ' + nDataCodes.readQuery,
});
throw new HTTPError({ statusCode: HttpStatus.INTERNAL_SERVER_ERROR });
```

## **12. Comments & Documentation**

### Section comments

```typescript
// Imports (at the top)
// Gathering -> Data
// Processing logic
// Success response
```

### Inline comments for complex logic

```typescript
// Converting -> Array into object for better performance
// Purpose -> Debugging
```

## **13. Type Annotations**

### Parameter types

```typescript
async create(modelName, data, t1?: sequelize.Transaction)
async findOne(modelName, options: ISequelizeFindOptions): Promise<any>
```

### Interface usage

```typescript
export interface ISequelizeFindOptions {
  attributes?: FindAttributeOptions | any;
  where?: WhereOptions;
  include?: Includeable[] | any;
  // ...
}
```

## **14. Configuration & Constants**

### Constant objects

```typescript
export const kUserStageEnums = {
  PHONE_VERIFICATION: 'PHONE VERIFICATION',
  BASIC_DETAILS: 'BASIC DETAILS',
  // ...
};
```

### Network configurations

```typescript
const nDataCodesBaseUrl = Env.neighbours.dataCodes.base_url;
export const nDataCodes = {
  bulkWrites: nDataCodesBaseUrl + 'v1/database/bulkWrites',
  // ...
};
```

## **15. Entity Patterns**

### Sequelize decorators

```typescript
@Table({})
export class loanTransaction extends Model<loanTransaction> {
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => registeredUsers)
  @Column({
    type: DataType.UUID,
    allowNull: false,
  })
  userId: string;

  @BelongsTo(() => registeredUsers, {
    foreignKey: 'userId',
    targetKey: 'id',
    constraints: false,
  })
  registeredUsers: registeredUsers;
}
```

### AfterFind hooks

```typescript
@AfterFind
static formatAfterFind(instances: loanTransaction[] | loanTransaction | any) {
  if (Array.isArray(instances)) {
    if (instances.length > 0) {
      instances.forEach((instance: any) => {
        if (instance.interestRate) {
          instance.interestRate = +instance.interestRate;
        }
      });
    }
  } else if (instances) {
    if (instances.interestRate) {
      instances.interestRate = +instances.interestRate;
    }
  }
}
```

## **16. Service Injection Patterns**

### Constructor injection

```typescript
@Injectable()
export class ServiceName {
  constructor(
    private readonly api: ApiService,
    private readonly query: ServiceQuery,
    private readonly pg: PgService,
  ) {}
}
```

## **17. Controller Patterns**

### Controller structure

```typescript
@Controller('endpoint')
export class ControllerName {
  constructor(private readonly service: ServiceName) {}

  @Post('methodName')
  async funMethodName(@Body() body) {
    return await this.service.methodName(body);
  }

  @Get('methodName')
  async funMethodName(@Query() query) {
    return await this.service.methodName(query);
  }
}
```

## **18. Validation Patterns**

### Parameter validation

```typescript
if (!reqData.query) {
  raiseParamMissing('query');
}

if (reqData.db_type != 'clickHouse') {
  throw HTTPError({ value: 'db_type' });
}
```

### Type checking

```typescript
const isReadOnly = reqData.readOnly == 'true' || reqData.readOnly == true;
if (typeof options.raw === 'undefined') options.raw = true;
```

## **19. Performance Optimization Patterns**

### Array to object conversion for lookups

```typescript
// Converting -> Array into object for better performance of linking
const loanData = {};
loanList.forEach((el) => {
  loanData[el.id] = el;
});
```

### Filtering null values

```typescript
let cibilIds = loanList.map((el) => el.cibilId);
cibilIds = cibilIds.filter((el) => el);
```

## **20. Best Practices**

1. **Always use `const` for immutable values, `let` for mutable**
2. **Use meaningful variable names**: `loanData` instead of `data`
3. **Add comments for complex business logic**
4. **Use async/await instead of Promises**
5. **Validate parameters at the beginning of methods**
6. **Use try-catch for database operations**
7. **Return consistent response formats**
8. **Use proper HTTP status codes for errors**
9. **Log errors with context information**
10. **Use optional chaining (`?.`) for safe property access**

---

**Follow these patterns consistently for maintainable and readable code that matches the existing codebase standards.**
